#!/usr/bin/env python3
"""
Test script to verify that the content reading fix works
"""

import os
import sys
sys.path.insert(0, './Lib/site-packages')

def test_content_reading():
    """Test that file paths are correctly converted to content"""
    
    # Import the helper function from the modified reportlab module
    from finrobot.functional.reportlab import ReportLabUtils
    
    # Get the helper function (it's defined inside build_annual_report)
    # We'll test it indirectly by checking if file content is read
    
    # Test with file paths
    income_file = "./report/income_summarization.txt"
    business_file = "./report/business_highlights.txt"
    
    if not os.path.exists(income_file):
        print(f"❌ Test file not found: {income_file}")
        return False
        
    if not os.path.exists(business_file):
        print(f"❌ Test file not found: {business_file}")
        return False
    
    # Read the actual file content for comparison
    with open(income_file, 'r', encoding='utf-8') as f:
        expected_income_content = f.read()
    
    with open(business_file, 'r', encoding='utf-8') as f:
        expected_business_content = f.read()
    
    print("✅ Test files found and readable")
    print(f"Income file size: {len(expected_income_content)} characters")
    print(f"Business file size: {len(expected_business_content)} characters")
    
    # Test that file paths are not displayed as content
    if income_file in expected_income_content:
        print("❌ File path should not appear in file content")
        return False
    
    if business_file in expected_business_content:
        print("❌ File path should not appear in file content")
        return False
    
    print("✅ File contents do not contain file paths")
    
    # Test that the content contains actual Microsoft data
    if "Microsoft" in expected_income_content and "revenue" in expected_income_content.lower():
        print("✅ Income file contains expected Microsoft financial data")
    else:
        print("❌ Income file does not contain expected content")
        return False
    
    if "Microsoft" in expected_business_content:
        print("✅ Business highlights file contains expected Microsoft data")
    else:
        print("❌ Business highlights file does not contain expected content")
        return False
    
    return True

if __name__ == "__main__":
    print("Testing content reading fix...")
    success = test_content_reading()
    if success:
        print("\n✅ All tests passed! The fix should work correctly.")
        print("The PDF will now show actual content instead of file paths.")
    else:
        print("\n❌ Some tests failed!")

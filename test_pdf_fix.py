#!/usr/bin/env python3
"""
Test script to verify the PDF generation fix
"""

import os
import sys
sys.path.insert(0, './Lib/site-packages')

from finrobot.functional.reportlab import ReportLabUtils


proxy = "http://127.0.0.1:7890"
os.environ["http_proxy"] = proxy
os.environ["https_proxy"] = proxy

def test_pdf_generation():
    """Test PDF generation with file paths as parameters"""
    
    # Test parameters
    ticker_symbol = "MSFT"
    save_path = "./report"
    
    # Use file paths (this is what was causing the issue)
    income_summarization = "./report/income_summarization.txt"
    business_highlights = "./report/business_highlights.txt"
    company_description = "./report/company_description.txt"
    risk_assessment = "./report/risk_assessment.txt"
    
    share_performance_image_path = "./report/share_performance.png"
    pe_eps_performance_image_path = "./report/pe_eps_performance.png"
    filing_date = "2023-07-31"
    
    # Check if required files exist
    required_files = [
        income_summarization,
        business_highlights,
        company_description,
        risk_assessment,
        share_performance_image_path,
        pe_eps_performance_image_path
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("Missing required files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    try:
        # Call the build_annual_report static method
        result = ReportLabUtils.build_annual_report(
            ticker_symbol=ticker_symbol,
            save_path=save_path,
            income_summarization=income_summarization,
            business_highlights=business_highlights,
            company_description=company_description,
            risk_assessment=risk_assessment,
            share_performance_image_path=share_performance_image_path,
            pe_eps_performance_image_path=pe_eps_performance_image_path,
            filing_date=filing_date
        )
        
        print(f"PDF generation result: {result}")
        
        # Check if PDF was created
        pdf_path = os.path.join(save_path, f"{ticker_symbol}_report.pdf")
        if os.path.exists(pdf_path):
            print(f"PDF successfully created at: {pdf_path}")
            return True
        else:
            print("PDF file was not created")
            return False
            
    except Exception as e:
        print(f"Error during PDF generation: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing PDF generation fix...")
    success = test_pdf_generation()
    if success:
        print("✅ Test passed! PDF generation works correctly with file paths.")
    else:
        print("❌ Test failed! There are still issues with PDF generation.")

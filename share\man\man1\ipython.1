.\"                                      Hey, EMACS: -*- nroff -*-
.\" First parameter, NAME, should be all caps
.\" Second parameter, SECTION, should be 1-8, maybe w/ subsection
.\" other parameters are allowed: see man(7), man(1)
.TH IPYTHON 1 "July 15, 2011"
.\" Please adjust this date whenever revising the manpage.
.\"
.\" Some roff macros, for reference:
.\" .nh        disable hyphenation
.\" .hy        enable hyphenation
.\" .ad l      left justify
.\" .ad b      justify to both left and right margins
.\" .nf        disable filling
.\" .fi        enable filling
.\" .br        insert line break
.\" .sp <n>    insert n+1 empty lines
.\" for manpage-specific macros, see man(7) and groff_man(7)
.\" .SH        section heading
.\" .SS        secondary section heading
.\"
.\"
.\" To preview this page as plain text: nroff -man ipython.1
.\"
.SH NAME
ipython \- Tools for Interactive Computing in Python.
.SH SYNOPSIS
.B ipython
.RI [ options ] " files" ...

.B ipython subcommand
.RI [ options ] ...

.SH DESCRIPTION
An interactive Python shell with automatic history (input and output), dynamic
object introspection, easier configuration, command completion, access to the
system shell, integration with numerical and scientific computing tools,
web notebook, Qt console, and more.

For more information on how to use IPython, see 'ipython \-\-help',
or 'ipython \-\-help\-all' for all available command\(hyline options.

.SH "ENVIRONMENT VARIABLES"
.sp
.PP
\fIIPYTHONDIR\fR
.RS 4
This is the location where IPython stores all its configuration files.  The default
is $HOME/.ipython if IPYTHONDIR is not defined.

You can see the computed value of IPYTHONDIR with `ipython locate`.

.SH FILES

IPython uses various configuration files stored in profiles within IPYTHONDIR.
To generate the default configuration files and start configuring IPython,
do 'ipython profile create', and edit '*_config.py' files located in
IPYTHONDIR/profile_default.

.SH AUTHORS
IPython is written by the IPython Development Team <https://github.com/ipython/ipython>.
